#!/usr/bin/env python3
"""
Fix NumPy Version Conflict
=========================

This script fixes the NumPy version conflict between ChatTTS (requires numpy>=2.0)
and Degirum (requires numpy<2.0) by downgrading to a compatible version.
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

def fix_numpy_conflict():
    """Fix NumPy version conflict"""
    try:
        # Check current NumPy version
        import numpy
        current_version = numpy.__version__
        logger.info(f"Current NumPy version: {current_version}")
        
        # Check if we have incompatible version (2.x)
        major_version = int(current_version.split('.')[0])
        
        if major_version >= 2:
            logger.warning("NumPy 2.x detected. Degirum requires NumPy < 2.0")
            logger.info("Downgrading NumPy to compatible version...")
            
            # Uninstall current NumPy
            subprocess.run([
                sys.executable, "-m", "pip", "uninstall", "numpy", "-y"
            ], check=True)
            
            # Install compatible version
            subprocess.run([
                sys.executable, "-m", "pip", "install", "numpy>=1.22,<2.0.0"
            ], check=True)
            
            # Verify installation
            import importlib
            importlib.reload(numpy)
            new_version = numpy.__version__
            logger.info(f"NumPy downgraded to: {new_version}")
            
            # Check if Degirum still works
            try:
                import degirum
                logger.info("Degirum compatibility verified")
            except ImportError as e:
                logger.warning(f"Degirum import issue: {e}")
            
            return True
        else:
            logger.info("NumPy version is compatible with Degirum")
            return True
            
    except ImportError:
        logger.error("NumPy not installed")
        return False
    except Exception as e:
        logger.error(f"Error fixing NumPy conflict: {e}")
        return False

def main():
    """Main function"""
    print("NumPy Conflict Fix")
    print("==================")
    
    if fix_numpy_conflict():
        print("\n✅ NumPy conflict resolved!")
        print("You can now use both Degirum and TTS engines.")
        print("\nNext steps:")
        print("1. Test Degirum: python -c 'import degirum; print(\"Degirum OK\")'")
        print("2. Test TTS: python test_tts.py")
    else:
        print("\n❌ Failed to resolve NumPy conflict")
        print("You may need to manually install compatible versions:")
        print("pip install numpy>=1.22,<2.0.0")

if __name__ == "__main__":
    main()
