# High-Performance TTS Engine Requirements
# =======================================

# Core TTS Engines (install based on your preference)

# RealtimeTTS - Recommended for best performance and features
# Full installation with all engines
realtimetts[all]>=0.3.0

# Alternative: Install specific engines only
# realtimetts[coqui]  # For local neural TTS
# realtimetts[azure,elevenlabs,openai]  # For cloud services

# ChatTTS - For conversational TTS
ChatTTS>=0.1.0
torch>=2.1.0
torchaudio>=2.1.0

# Piper TTS - Lightweight and fast
piper-tts>=1.2.0

# System TTS fallback
pyttsx3>=2.90

# Audio processing and utilities
numpy<2.0.0
soundfile>=0.12.0
librosa>=0.9.2

# Optional: GPU acceleration (CUDA)
# Uncomment if you have NVIDIA GPU and want GPU acceleration
# torch==2.3.1+cu121 --index-url https://download.pytorch.org/whl/cu121
# torchaudio==2.3.1+cu121 --index-url https://download.pytorch.org/whl/cu121

# Optional: Additional audio codecs
# ffmpeg-python>=0.2.0

# Development and testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
