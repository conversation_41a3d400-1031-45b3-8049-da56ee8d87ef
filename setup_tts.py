#!/usr/bin/env python3
"""
TTS Engine Setup Script
=======================

This script helps you set up the high-performance TTS engine with the best
configuration for your system.
"""

import os
import sys
import subprocess
import platform
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("Python 3.8 or higher is required")
        return False
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_virtual_environment():
    """Check if running in virtual environment"""
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    if in_venv:
        logger.info("Running in virtual environment")
        return True
    else:
        logger.warning("Not running in virtual environment. Consider using cam-env.")
        return False

def install_system_dependencies():
    """Install system-level dependencies"""
    system = platform.system().lower()
    logger.info(f"Detected system: {system}")
    
    try:
        if system == "linux":
            # Install audio dependencies for Linux
            logger.info("Installing Linux audio dependencies...")
            subprocess.run([
                "sudo", "apt", "update"
            ], check=True)
            
            subprocess.run([
                "sudo", "apt", "install", "-y",
                "portaudio19-dev",  # For audio I/O
                "espeak", "espeak-data",  # For espeak TTS
                "ffmpeg",  # For audio processing
                "alsa-utils",  # For ALSA audio
                "pulseaudio-utils",  # For PulseAudio
                "libsndfile1-dev",  # For audio file handling
            ], check=True)
            
        elif system == "darwin":  # macOS
            logger.info("Installing macOS audio dependencies...")
            # Check if Homebrew is available
            try:
                subprocess.run(["brew", "--version"], check=True, capture_output=True)
                subprocess.run([
                    "brew", "install",
                    "portaudio",
                    "ffmpeg",
                    "espeak"
                ], check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.warning("Homebrew not found. Please install manually:")
                logger.warning("1. Install Homebrew: https://brew.sh/")
                logger.warning("2. Run: brew install portaudio ffmpeg espeak")
                
        elif system == "windows":
            logger.info("Windows detected. Some dependencies may need manual installation.")
            logger.info("Consider installing:")
            logger.info("1. Microsoft Visual C++ Redistributable")
            logger.info("2. Windows Media Feature Pack (for audio codecs)")
            
        logger.info("System dependencies installation completed")
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install system dependencies: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error installing system dependencies: {e}")
        return False
    
    return True

def fix_numpy_conflict():
    """Fix NumPy version conflict with Degirum"""
    try:
        import numpy
        current_version = numpy.__version__
        logger.info(f"Current NumPy version: {current_version}")

        # Check if we have incompatible version
        major, minor = map(int, current_version.split('.')[:2])
        if major >= 2:
            logger.warning("NumPy 2.x detected, downgrading for Degirum compatibility...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "numpy>=1.22,<2.0.0", "--force-reinstall"
            ], check=True)
            logger.info("NumPy downgraded successfully")
            return True
    except Exception as e:
        logger.warning(f"Could not check/fix NumPy version: {e}")

    return True

def install_python_dependencies():
    """Install Python dependencies"""
    logger.info("Installing Python dependencies...")

    # Read requirements
    req_file = Path(__file__).parent / "tts_requirements.txt"
    if not req_file.exists():
        logger.error("tts_requirements.txt not found")
        return False

    try:
        # Install basic requirements first (compatible with Degirum)
        basic_packages = [
            "numpy>=1.22,<2.0.0",  # Compatible with Degirum
            "pyttsx3>=2.90",
            "soundfile>=0.12.0",
        ]
        
        for package in basic_packages:
            logger.info(f"Installing {package}...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], check=True)
        
        # Try to install RealtimeTTS (may fail on some systems)
        try:
            logger.info("Installing RealtimeTTS...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "realtimetts[coqui]"
            ], check=True)
            logger.info("RealtimeTTS installed successfully")
        except subprocess.CalledProcessError:
            logger.warning("RealtimeTTS installation failed. Will use fallback engines.")
        
        # Try to install ChatTTS
        try:
            logger.info("Installing ChatTTS...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "ChatTTS", "torch", "torchaudio"
            ], check=True)
            logger.info("ChatTTS installed successfully")
        except subprocess.CalledProcessError:
            logger.warning("ChatTTS installation failed. Will use fallback engines.")
        
        logger.info("Python dependencies installation completed")
        return True
        
    except Exception as e:
        logger.error(f"Failed to install Python dependencies: {e}")
        return False

def test_installation():
    """Test the TTS installation"""
    logger.info("Testing TTS installation...")
    
    try:
        # Import and test the TTS engine
        from tts_engine import HighPerformanceTTS, TTSConfig, TTSEngine
        
        # Test system TTS (should always work)
        logger.info("Testing system TTS...")
        config = TTSConfig(engine=TTSEngine.SYSTEM)
        tts = HighPerformanceTTS(config)
        logger.info("System TTS: OK")
        
        # Test RealtimeTTS if available
        try:
            config = TTSConfig(engine=TTSEngine.REALTIMETTS)
            tts = HighPerformanceTTS(config)
            logger.info("RealtimeTTS: OK")
        except Exception:
            logger.warning("RealtimeTTS: Not available")
        
        # Test ChatTTS if available
        try:
            config = TTSConfig(engine=TTSEngine.CHATTTS)
            tts = HighPerformanceTTS(config)
            logger.info("ChatTTS: OK")
        except Exception:
            logger.warning("ChatTTS: Not available")
        
        logger.info("Installation test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Installation test failed: {e}")
        return False

def create_example_script():
    """Create a simple test script"""
    script_content = '''#!/usr/bin/env python3
"""
Simple TTS Test Script
"""

from tts_engine import quick_speak, create_tts_engine

def main():
    print("Testing TTS Engine...")
    
    # Simple test
    quick_speak("Hello! TTS engine is working correctly.")
    
    # Test with different settings
    tts = create_tts_engine("system", speed=1.2, volume=0.8)
    tts.speak("This is a test with custom speed and volume.")
    
    print("TTS test completed!")

if __name__ == "__main__":
    main()
'''
    
    script_path = Path(__file__).parent / "test_tts.py"
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # Make executable on Unix systems
    if platform.system() != "Windows":
        os.chmod(script_path, 0o755)
    
    logger.info(f"Created test script: {script_path}")

def main():
    """Main setup function"""
    print("High-Performance TTS Engine Setup")
    print("=================================")
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    check_virtual_environment()
    
    # Install dependencies
    logger.info("Starting installation process...")
    
    # System dependencies
    if input("Install system dependencies? (y/n): ").lower().startswith('y'):
        if not install_system_dependencies():
            logger.error("System dependency installation failed")
            sys.exit(1)
    
    # Python dependencies
    if not install_python_dependencies():
        logger.error("Python dependency installation failed")
        sys.exit(1)

    # Fix NumPy conflict if needed
    fix_numpy_conflict()
    
    # Test installation
    if not test_installation():
        logger.error("Installation test failed")
        sys.exit(1)
    
    # Create test script
    create_example_script()
    
    print("\n" + "="*50)
    print("Setup completed successfully!")
    print("="*50)
    print("\nNext steps:")
    print("1. Test the installation: python test_tts.py")
    print("2. Run examples: python tts_examples.py")
    print("3. Use in your code: from tts_engine import quick_speak")
    print("\nBasic usage:")
    print("  from tts_engine import quick_speak")
    print("  quick_speak('Hello, world!')")
    print("\nAdvanced usage:")
    print("  from tts_engine import create_tts_engine")
    print("  tts = create_tts_engine('realtimetts', speed=1.2)")
    print("  tts.speak('Advanced TTS usage')")

if __name__ == "__main__":
    main()
