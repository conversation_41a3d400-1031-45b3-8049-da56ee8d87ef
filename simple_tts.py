#!/usr/bin/env python3
"""
Simple High-Performance TTS Engine
==================================

A lightweight, reliable TTS engine that works on Raspberry Pi and other systems.
Focuses on compatibility and ease of use.
"""

import os
import sys
import subprocess
import tempfile
import logging
import threading
import time
from pathlib import Path
from typing import Optional, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(name)s: %(message)s')
logger = logging.getLogger(__name__)

class SimpleTTS:
    """
    Simple, reliable Text-to-Speech engine.
    
    Uses system tools for maximum compatibility:
    - espeak/espeak-ng for speech synthesis
    - aplay for audio playback on Linux
    - Built-in system commands for other platforms
    """
    
    def __init__(self, speed: float = 1.0, volume: float = 1.0, voice: str = "en"):
        """
        Initialize the TTS engine.
        
        Args:
            speed: Speech speed (0.5-2.0, default 1.0)
            volume: Volume level (0.0-1.0, default 1.0)
            voice: Voice/language code (default "en")
        """
        self.speed = max(0.5, min(2.0, speed))
        self.volume = max(0.0, min(1.0, volume))
        self.voice = voice
        self.is_playing = False
        self._stop_event = threading.Event()
        
        # Check available TTS methods
        self.tts_method = self._detect_tts_method()
        logger.info(f"Using TTS method: {self.tts_method}")
    
    def _detect_tts_method(self) -> str:
        """Detect the best available TTS method"""
        
        # Check for espeak-ng (preferred)
        if self._command_exists("espeak-ng"):
            return "espeak-ng"
        
        # Check for espeak (fallback)
        if self._command_exists("espeak"):
            return "espeak"
        
        # Check for festival
        if self._command_exists("festival"):
            return "festival"
        
        # Check for say (macOS)
        if self._command_exists("say"):
            return "say"
        
        # Check for PowerShell (Windows)
        if sys.platform.startswith('win') and self._command_exists("powershell"):
            return "powershell"
        
        # Fallback to Python TTS if available
        try:
            import pyttsx3
            return "pyttsx3"
        except ImportError:
            pass
        
        logger.warning("No TTS method found. Speech will be simulated.")
        return "none"
    
    def _command_exists(self, command: str) -> bool:
        """Check if a command exists in the system PATH"""
        try:
            subprocess.run([command, "--version"], 
                         capture_output=True, check=True, timeout=5)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            try:
                # Try alternative check
                subprocess.run(["which", command], 
                             capture_output=True, check=True, timeout=5)
                return True
            except:
                return False
    
    def speak(self, text: str, blocking: bool = True) -> bool:
        """
        Convert text to speech and play it.
        
        Args:
            text: Text to speak
            blocking: If True, wait for speech to complete
            
        Returns:
            True if successful, False otherwise
        """
        if not text.strip():
            logger.warning("Empty text provided")
            return False
        
        try:
            if blocking:
                return self._speak_blocking(text)
            else:
                thread = threading.Thread(target=self._speak_blocking, args=(text,), daemon=True)
                thread.start()
                return True
        except Exception as e:
            logger.error(f"TTS error: {e}")
            return False
    
    def _speak_blocking(self, text: str) -> bool:
        """Internal method for blocking speech"""
        self.is_playing = True
        self._stop_event.clear()
        
        try:
            if self.tts_method == "espeak-ng":
                return self._speak_espeak_ng(text)
            elif self.tts_method == "espeak":
                return self._speak_espeak(text)
            elif self.tts_method == "festival":
                return self._speak_festival(text)
            elif self.tts_method == "say":
                return self._speak_say(text)
            elif self.tts_method == "powershell":
                return self._speak_powershell(text)
            elif self.tts_method == "pyttsx3":
                return self._speak_pyttsx3(text)
            else:
                # Simulate speech for testing
                logger.info(f"[SIMULATED SPEECH]: {text}")
                time.sleep(len(text) * 0.05)  # Simulate speech duration
                return True
        finally:
            self.is_playing = False
    
    def _speak_espeak_ng(self, text: str) -> bool:
        """Speak using espeak-ng"""
        try:
            # Calculate speed for espeak (words per minute)
            wpm = int(175 * self.speed)  # Base 175 WPM
            amplitude = int(100 * self.volume)
            
            cmd = [
                "espeak-ng",
                "-v", self.voice,
                "-s", str(wpm),
                "-a", str(amplitude),
                text
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"espeak-ng error: {e}")
            return False
    
    def _speak_espeak(self, text: str) -> bool:
        """Speak using espeak"""
        try:
            # Calculate speed for espeak (words per minute)
            wpm = int(175 * self.speed)
            amplitude = int(100 * self.volume)
            
            cmd = [
                "espeak",
                "-v", self.voice,
                "-s", str(wpm),
                "-a", str(amplitude),
                text
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"espeak error: {e}")
            return False
    
    def _speak_festival(self, text: str) -> bool:
        """Speak using festival"""
        try:
            # Create temporary file for festival
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write(text)
                temp_file = f.name
            
            try:
                cmd = ["festival", "--tts", temp_file]
                result = subprocess.run(cmd, capture_output=True, timeout=30)
                return result.returncode == 0
            finally:
                os.unlink(temp_file)
                
        except Exception as e:
            logger.error(f"festival error: {e}")
            return False
    
    def _speak_say(self, text: str) -> bool:
        """Speak using macOS say command"""
        try:
            rate = int(200 * self.speed)  # Base 200 WPM for say
            
            cmd = [
                "say",
                "-r", str(rate),
                "-v", self.voice if self.voice != "en" else "Alex",
                text
            ]
            
            result = subprocess.run(cmd, capture_output=True, timeout=30)
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"say error: {e}")
            return False
    
    def _speak_powershell(self, text: str) -> bool:
        """Speak using Windows PowerShell"""
        try:
            # PowerShell command for TTS
            ps_script = f'''
            Add-Type -AssemblyName System.Speech
            $synth = New-Object System.Speech.Synthesis.SpeechSynthesizer
            $synth.Rate = {int(-2 + 4 * self.speed)}
            $synth.Volume = {int(100 * self.volume)}
            $synth.Speak("{text}")
            '''
            
            cmd = ["powershell", "-Command", ps_script]
            result = subprocess.run(cmd, capture_output=True, timeout=30)
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"PowerShell TTS error: {e}")
            return False
    
    def _speak_pyttsx3(self, text: str) -> bool:
        """Speak using pyttsx3 (fallback)"""
        try:
            import pyttsx3
            
            engine = pyttsx3.init()
            engine.setProperty('rate', int(200 * self.speed))
            engine.setProperty('volume', self.volume)
            
            # Try to set voice if available
            voices = engine.getProperty('voices')
            if voices:
                for voice in voices:
                    if self.voice.lower() in voice.name.lower():
                        engine.setProperty('voice', voice.id)
                        break
            
            engine.say(text)
            engine.runAndWait()
            return True
            
        except Exception as e:
            logger.error(f"pyttsx3 error: {e}")
            return False
    
    def speak_async(self, text: str) -> bool:
        """
        Speak text asynchronously (non-blocking).
        
        Args:
            text: Text to speak
            
        Returns:
            True if started successfully
        """
        return self.speak(text, blocking=False)
    
    def stop(self):
        """Stop current speech"""
        self._stop_event.set()
        self.is_playing = False
        
        # Try to kill espeak processes
        try:
            subprocess.run(["pkill", "-f", "espeak"], capture_output=True)
        except:
            pass
    
    def set_speed(self, speed: float):
        """Set speech speed (0.5-2.0)"""
        self.speed = max(0.5, min(2.0, speed))
    
    def set_volume(self, volume: float):
        """Set volume (0.0-1.0)"""
        self.volume = max(0.0, min(1.0, volume))
    
    def set_voice(self, voice: str):
        """Set voice/language"""
        self.voice = voice
    
    def get_available_voices(self) -> list:
        """Get list of available voices"""
        voices = []
        
        if self.tts_method in ["espeak", "espeak-ng"]:
            try:
                result = subprocess.run([self.tts_method, "--voices"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')[1:]  # Skip header
                    for line in lines:
                        parts = line.split()
                        if len(parts) >= 4:
                            voices.append(parts[1])  # Language code
            except:
                pass
        
        return voices or ["en", "en-us", "en-gb"]  # Default voices


# Convenience functions
def quick_speak(text: str, **kwargs) -> bool:
    """
    Quick text-to-speech function.
    
    Args:
        text: Text to speak
        **kwargs: TTS options (speed, volume, voice)
        
    Returns:
        True if successful
    """
    tts = SimpleTTS(**kwargs)
    return tts.speak(text)


def create_tts(speed: float = 1.0, volume: float = 1.0, voice: str = "en") -> SimpleTTS:
    """
    Create a TTS engine with specified settings.
    
    Args:
        speed: Speech speed (0.5-2.0)
        volume: Volume (0.0-1.0)
        voice: Voice/language code
        
    Returns:
        SimpleTTS instance
    """
    return SimpleTTS(speed=speed, volume=volume, voice=voice)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Simple High-Performance TTS")
    parser.add_argument("text", nargs="?", 
                       default="Hello! This is the simple TTS engine working correctly.",
                       help="Text to speak")
    parser.add_argument("--speed", type=float, default=1.0, help="Speech speed (0.5-2.0)")
    parser.add_argument("--volume", type=float, default=1.0, help="Volume (0.0-1.0)")
    parser.add_argument("--voice", default="en", help="Voice/language code")
    parser.add_argument("--async-mode", action="store_true", help="Async playback")
    parser.add_argument("--list-voices", action="store_true", help="List available voices")
    parser.add_argument("--test", action="store_true", help="Run test suite")
    
    args = parser.parse_args()
    
    try:
        tts = SimpleTTS(speed=args.speed, volume=args.volume, voice=args.voice)
        
        if args.list_voices:
            voices = tts.get_available_voices()
            print("Available voices:")
            for voice in voices:
                print(f"  - {voice}")
        elif args.test:
            print("Running TTS test suite...")
            
            # Test basic functionality
            print("1. Basic speech test...")
            success = tts.speak("This is a basic speech test.")
            print(f"   Result: {'✅ Success' if success else '❌ Failed'}")
            
            # Test speed variations
            print("2. Speed variation test...")
            for speed in [0.8, 1.0, 1.5]:
                tts.set_speed(speed)
                success = tts.speak(f"Speed test at {speed} times normal.")
                print(f"   Speed {speed}: {'✅ Success' if success else '❌ Failed'}")
            
            # Test async
            print("3. Async speech test...")
            tts.set_speed(1.0)
            success = tts.speak_async("This is async speech.")
            print(f"   Async: {'✅ Started' if success else '❌ Failed'}")
            time.sleep(3)
            
            print("Test suite completed!")
        else:
            print(f"Speaking: {args.text}")
            if args.async_mode:
                success = tts.speak_async(args.text)
                if success:
                    print("Async speech started...")
                    time.sleep(5)  # Wait for speech to complete
            else:
                success = tts.speak(args.text)
            
            print(f"Result: {'✅ Success' if success else '❌ Failed'}")
            
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)
