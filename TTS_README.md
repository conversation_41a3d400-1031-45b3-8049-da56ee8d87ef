# High-Performance Text-to-Speech Engine

A reusable, high-performance TTS engine with multiple backend support, designed for easy integration and optimal performance.

## Features

- **Multiple TTS Engines**: RealtimeTTS, ChatTTS, Piper, System TTS
- **High Performance**: Optimized for speed and quality
- **Easy to Use**: Simple API for quick integration
- **Async Support**: Non-blocking speech synthesis
- **Voice Customization**: Speed, volume, voice selection
- **Caching**: Automatic caching for improved performance
- **Cross-Platform**: Works on Linux, macOS, and Windows
- **Fallback Support**: Graceful degradation if engines fail

## Quick Start

### 1. Installation

```bash
# Activate your virtual environment (recommended)
source cam-env/bin/activate

# Run the setup script
python setup_tts.py

# Or install manually
pip install -r tts_requirements.txt
```

### 2. Basic Usage

```python
from tts_engine import quick_speak

# Simple usage
quick_speak("Hello, world!")
```

### 3. Advanced Usage

```python
from tts_engine import create_tts_engine

# Create TTS engine with custom settings
tts = create_tts_engine("realtimetts", speed=1.2, volume=0.8)

# Speak text
tts.speak("This is advanced TTS usage")

# Async speech (non-blocking)
tts.speak_async("This plays in the background")
```

## Supported Engines

### 1. RealtimeTTS (Recommended)
- **Best Performance**: Low latency, high quality
- **Multiple Backends**: Coqui, Azure, ElevenLabs, OpenAI
- **Real-time Streaming**: Perfect for live applications

```python
tts = create_tts_engine("realtimetts")
```

### 2. ChatTTS
- **Conversational**: Optimized for dialogue
- **Natural Speech**: Expressive and natural-sounding
- **Prosody Control**: Fine-grained control over speech

```python
tts = create_tts_engine("chattts")
```

### 3. Piper TTS
- **Lightweight**: Fast and efficient
- **Local Processing**: No internet required
- **Multiple Languages**: Wide language support

```python
tts = create_tts_engine("piper")
```

### 4. System TTS
- **Always Available**: Built into OS
- **Reliable Fallback**: Works everywhere
- **Voice Selection**: Multiple system voices

```python
tts = create_tts_engine("system")
```

## API Reference

### Quick Functions

```python
from tts_engine import quick_speak, create_tts_engine

# One-liner TTS
quick_speak("Hello!")

# Create engine with options
tts = create_tts_engine("realtimetts", speed=1.5, voice="female")
```

### HighPerformanceTTS Class

```python
from tts_engine import HighPerformanceTTS, TTSConfig, TTSEngine

# Create with configuration
config = TTSConfig(
    engine=TTSEngine.REALTIMETTS,
    speed=1.2,
    volume=0.8,
    enable_cache=True
)
tts = HighPerformanceTTS(config)

# Basic methods
tts.speak("Hello")                    # Blocking speech
tts.speak_async("Hello")              # Non-blocking speech
tts.stop()                           # Stop current speech

# Customization
tts.set_speed(1.5)                   # Change speed
tts.set_volume(0.7)                  # Change volume
tts.set_voice("female")              # Change voice

# Utility methods
voices = tts.get_available_voices()   # List voices
tts.clear_cache()                    # Clear cache
```

## Configuration Options

```python
from tts_engine import TTSConfig, TTSEngine

config = TTSConfig(
    engine=TTSEngine.REALTIMETTS,    # Engine type
    voice="female",                   # Voice name (engine-dependent)
    speed=1.0,                       # Speech speed (0.5-2.0)
    volume=1.0,                      # Volume (0.0-1.0)
    cache_dir="/path/to/cache",      # Cache directory
    enable_cache=True,               # Enable caching
    sample_rate=24000,               # Audio sample rate
    quality="medium"                 # Quality: low, medium, high
)
```

## Examples

### Integration with Your Application

```python
from tts_engine import create_tts_engine

# Create TTS engine once (reuse throughout app)
tts = create_tts_engine("realtimetts", speed=1.1)

# Use for notifications
def on_detection(object_name):
    message = f"Detected {object_name}"
    tts.speak_async(message)  # Non-blocking
```

### Voice Comparison

```python
tts = create_tts_engine("system")
voices = tts.get_available_voices()

for voice in voices[:3]:  # Test first 3 voices
    tts.set_voice(voice)
    tts.speak(f"This is the {voice} voice")
```

### Performance Testing

```python
import time

tts = create_tts_engine("realtimetts")
text = "Performance test message"

# Time speech generation
start = time.time()
tts.speak(text)
duration = time.time() - start
print(f"Speech took {duration:.2f} seconds")
```

## Installation Details

### System Dependencies

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install portaudio19-dev espeak ffmpeg alsa-utils
```

**macOS:**
```bash
brew install portaudio ffmpeg espeak
```

**Windows:**
- Install Microsoft Visual C++ Redistributable
- Windows Media Feature Pack (for audio codecs)

### Python Dependencies

**Core (always installed):**
- `pyttsx3` - System TTS
- `numpy` - Numerical operations
- `soundfile` - Audio file handling

**Optional (high-performance engines):**
- `realtimetts[all]` - RealtimeTTS with all engines
- `ChatTTS` - Conversational TTS
- `torch`, `torchaudio` - PyTorch for neural TTS
- `piper-tts` - Piper TTS engine

## Troubleshooting

### Common Issues

1. **"No module named 'RealtimeTTS'"**
   ```bash
   pip install realtimetts[all]
   ```

2. **Audio playback issues on Linux**
   ```bash
   sudo apt install pulseaudio-utils alsa-utils
   ```

3. **Permission errors**
   ```bash
   # Use virtual environment
   source cam-env/bin/activate
   ```

4. **Slow performance**
   - Enable caching: `TTSConfig(enable_cache=True)`
   - Use GPU acceleration if available
   - Try different engines for your use case

### Performance Tips

1. **Use Caching**: Enable for repeated text
2. **Choose Right Engine**: RealtimeTTS for speed, ChatTTS for quality
3. **Reuse Engine**: Create once, use multiple times
4. **Async for UI**: Use `speak_async()` to avoid blocking
5. **GPU Acceleration**: Install CUDA versions of PyTorch

## Testing

```bash
# Run setup and test
python setup_tts.py

# Test installation
python test_tts.py

# Run examples
python tts_examples.py

# Command line usage
python tts_engine.py "Hello, world!" --engine realtimetts --speed 1.2
```

## Integration Examples

### With Your Video Processing App

```python
from tts_engine import create_tts_engine

class VideoProcessor:
    def __init__(self):
        self.tts = create_tts_engine("realtimetts", speed=1.1)
    
    def on_face_detected(self, name):
        self.tts.speak_async(f"Hello, {name}")
    
    def on_object_detected(self, obj_type, confidence):
        if confidence > 0.8:
            self.tts.speak_async(f"Detected {obj_type}")
```

### With Hailo AI Detection

```python
# In your showcase framework
from tts_engine import create_tts_engine

class TTSProcessor(FrameProcessor):
    def __init__(self):
        self.tts = create_tts_engine("realtimetts")
        self.last_announcement = 0
    
    def process(self, frame_rgb, detections):
        current_time = time.time()
        if current_time - self.last_announcement > 3:  # Throttle announcements
            if detections:
                obj_count = len(detections)
                self.tts.speak_async(f"Detected {obj_count} objects")
                self.last_announcement = current_time
        return frame_rgb
```

## License

This TTS engine wrapper is provided as-is for educational and development purposes. Individual TTS engines may have their own licenses.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Run `python setup_tts.py` to verify installation
3. Test with `python tts_examples.py`
4. Check individual engine documentation for engine-specific issues
