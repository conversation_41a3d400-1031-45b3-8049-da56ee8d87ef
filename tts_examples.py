#!/usr/bin/env python3
"""
TTS Engine Examples
==================

This file demonstrates various ways to use the high-performance TTS engine.
"""

import time
import logging
from tts_engine import HighPerformanceTTS, TTSConfig, TTSEngine, create_tts_engine, quick_speak

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def example_basic_usage():
    """Basic TTS usage example"""
    print("\n=== Basic Usage Example ===")
    
    # Simple usage with default settings
    tts = HighPerformanceTTS()
    tts.speak("Hello! This is a basic text-to-speech example.")

def example_quick_speak():
    """Quick speak function example"""
    print("\n=== Quick Speak Example ===")
    
    # One-liner for simple TTS
    quick_speak("This is the quickest way to use text-to-speech!")

def example_engine_comparison():
    """Compare different TTS engines"""
    print("\n=== Engine Comparison Example ===")
    
    text = "This is a test of different TTS engines."
    engines = ["system", "realtimetts"]  # Add others as available
    
    for engine_name in engines:
        try:
            print(f"\nTesting {engine_name} engine...")
            tts = create_tts_engine(engine_name)
            tts.speak(text)
            time.sleep(1)  # Brief pause between engines
        except Exception as e:
            print(f"Failed to test {engine_name}: {e}")

def example_voice_customization():
    """Voice customization example"""
    print("\n=== Voice Customization Example ===")
    
    # Create TTS with custom settings
    config = TTSConfig(
        engine=TTSEngine.SYSTEM,  # Use system for voice selection
        speed=1.2,  # 20% faster
        volume=0.8  # 80% volume
    )
    
    tts = HighPerformanceTTS(config)
    
    # List available voices
    voices = tts.get_available_voices()
    if voices:
        print(f"Available voices: {voices[:3]}...")  # Show first 3
        
        # Try different voices
        for voice in voices[:2]:  # Test first 2 voices
            print(f"Testing voice: {voice}")
            tts.set_voice(voice)
            tts.speak(f"Hello, I am speaking with the {voice} voice.")
            time.sleep(1)
    else:
        print("No voices available for this engine")

def example_async_speech():
    """Asynchronous speech example"""
    print("\n=== Async Speech Example ===")
    
    tts = HighPerformanceTTS()
    
    # Start async speech
    print("Starting async speech...")
    tts.speak_async("This is asynchronous speech. I'm speaking while the program continues.")
    
    # Do other work while speaking
    for i in range(3):
        print(f"Doing other work... {i+1}")
        time.sleep(1)
    
    print("Async speech should be playing in background")

def example_speed_and_volume():
    """Speed and volume control example"""
    print("\n=== Speed and Volume Control Example ===")
    
    tts = HighPerformanceTTS()
    
    # Test different speeds
    speeds = [0.5, 1.0, 1.5]
    for speed in speeds:
        tts.set_speed(speed)
        tts.speak(f"This is speech at {speed} times normal speed.")
        time.sleep(0.5)
    
    # Reset speed and test volumes
    tts.set_speed(1.0)
    volumes = [0.3, 0.7, 1.0]
    for volume in volumes:
        tts.set_volume(volume)
        tts.speak(f"This is speech at {int(volume*100)} percent volume.")
        time.sleep(0.5)

def example_caching():
    """Caching example"""
    print("\n=== Caching Example ===")
    
    # Enable caching
    config = TTSConfig(enable_cache=True)
    tts = HighPerformanceTTS(config)
    
    text = "This text will be cached for faster playback next time."
    
    # First time - will generate and cache
    print("First playback (generating and caching)...")
    start_time = time.time()
    tts.speak(text)
    first_time = time.time() - start_time
    
    time.sleep(1)
    
    # Second time - will use cache
    print("Second playback (using cache)...")
    start_time = time.time()
    tts.speak(text)
    second_time = time.time() - start_time
    
    print(f"First time: {first_time:.2f}s, Second time: {second_time:.2f}s")
    if second_time < first_time:
        print("Cache improved performance!")

def example_error_handling():
    """Error handling example"""
    print("\n=== Error Handling Example ===")
    
    try:
        # Try to use an invalid engine
        config = TTSConfig(engine="invalid_engine")
        tts = HighPerformanceTTS(config)
    except Exception as e:
        print(f"Caught expected error: {e}")
    
    # The engine should fallback gracefully
    tts = HighPerformanceTTS()
    
    # Test with empty text
    tts.speak("")  # Should handle gracefully
    
    # Test with very long text
    long_text = "This is a very long text. " * 50
    tts.speak(long_text[:100] + "...")  # Truncate for demo

def example_integration_with_your_app():
    """Example of how to integrate TTS into your existing application"""
    print("\n=== Integration Example ===")
    
    # Simulate your application detecting something
    detected_objects = ["person", "car", "bicycle"]
    
    # Create TTS engine once (reuse throughout app)
    tts = create_tts_engine("realtimetts", speed=1.1, volume=0.9)
    
    # Use TTS for notifications
    for obj in detected_objects:
        message = f"Detected {obj}"
        print(f"Detection: {message}")
        tts.speak_async(message)  # Non-blocking so detection continues
        time.sleep(2)  # Simulate detection interval

def example_performance_test():
    """Performance testing example"""
    print("\n=== Performance Test Example ===")
    
    engines_to_test = ["system"]  # Add others as available
    test_text = "Performance test message."
    
    for engine_name in engines_to_test:
        try:
            print(f"\nTesting {engine_name} performance...")
            tts = create_tts_engine(engine_name)
            
            # Warm up
            tts.speak("Warming up...")
            
            # Time multiple generations
            times = []
            for i in range(3):
                start = time.time()
                tts.speak(f"{test_text} Number {i+1}.")
                times.append(time.time() - start)
            
            avg_time = sum(times) / len(times)
            print(f"Average time for {engine_name}: {avg_time:.2f}s")
            
        except Exception as e:
            print(f"Failed to test {engine_name}: {e}")

def main():
    """Run all examples"""
    print("High-Performance TTS Engine Examples")
    print("====================================")
    
    examples = [
        example_basic_usage,
        example_quick_speak,
        example_async_speech,
        example_speed_and_volume,
        example_voice_customization,
        example_caching,
        example_integration_with_your_app,
        example_error_handling,
        example_engine_comparison,
        example_performance_test,
    ]
    
    for example in examples:
        try:
            example()
            time.sleep(2)  # Pause between examples
        except KeyboardInterrupt:
            print("\nExamples interrupted by user")
            break
        except Exception as e:
            print(f"Example failed: {e}")
            continue
    
    print("\n=== Examples Complete ===")

if __name__ == "__main__":
    main()
