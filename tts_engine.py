#!/usr/bin/env python3
"""
High-Performance Text-to-Speech Engine
=====================================

A reusable, high-performance TTS engine with multiple backend support.
Designed for easy integration and optimal performance.

Features:
- Multiple TTS engines (RealtimeTTS, Piper, ChatTTS)
- Async and sync playback
- Voice customization
- Caching for performance
- Easy-to-use API

Usage:
    from tts_engine import TTSEngine
    
    # Simple usage
    tts = TTSEngine()
    tts.speak("Hello, world!")
    
    # Advanced usage
    tts = TTSEngine(engine="realtimetts", voice="female")
    tts.speak_async("This is asynchronous speech")
"""

import os
import sys
import time
import logging
import threading
import hashlib
from pathlib import Path
from typing import Optional, Union, Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum
import tempfile

# Configure logging
logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(name)s: %(message)s')
logger = logging.getLogger(__name__)

class TTSEngine(Enum):
    """Available TTS engines"""
    REALTIMETTS = "realtimetts"
    PIPER = "piper"
    CHATTTS = "chattts"
    SYSTEM = "system"  # OS built-in TTS

@dataclass
class TTSConfig:
    """Configuration for TTS engine"""
    engine: TTSEngine = TTSEngine.REALTIMETTS
    voice: Optional[str] = None
    speed: float = 1.0
    volume: float = 1.0
    cache_dir: Optional[str] = None
    enable_cache: bool = True
    sample_rate: int = 24000
    quality: str = "medium"  # low, medium, high
    
class HighPerformanceTTS:
    """
    High-performance Text-to-Speech engine with multiple backend support.
    
    This class provides a unified interface for various TTS engines,
    optimized for performance and ease of use.
    """
    
    def __init__(self, config: Optional[TTSConfig] = None):
        """
        Initialize the TTS engine.
        
        Args:
            config: TTS configuration. If None, uses default config.
        """
        self.config = config or TTSConfig()
        self.engine = None
        self.is_playing = False
        self._stop_event = threading.Event()
        self._cache = {}
        
        # Setup cache directory
        if self.config.enable_cache:
            if self.config.cache_dir:
                self.cache_dir = Path(self.config.cache_dir)
            else:
                self.cache_dir = Path.home() / ".tts_cache"
            self.cache_dir.mkdir(exist_ok=True)
        
        # Initialize the selected engine
        self._initialize_engine()
    
    def _initialize_engine(self):
        """Initialize the selected TTS engine"""
        try:
            if self.config.engine == TTSEngine.REALTIMETTS:
                self._init_realtimetts()
            elif self.config.engine == TTSEngine.PIPER:
                self._init_piper()
            elif self.config.engine == TTSEngine.CHATTTS:
                self._init_chattts()
            elif self.config.engine == TTSEngine.SYSTEM:
                self._init_system()
            else:
                raise ValueError(f"Unsupported engine: {self.config.engine}")
                
            logger.info(f"TTS engine initialized: {self.config.engine.value}")
            
        except Exception as e:
            logger.error(f"Failed to initialize {self.config.engine.value}: {e}")
            logger.info("Falling back to system TTS")
            self.config.engine = TTSEngine.SYSTEM
            self._init_system()
    
    def _init_realtimetts(self):
        """Initialize RealtimeTTS engine"""
        try:
            from RealtimeTTS import TextToAudioStream, CoquiEngine, SystemEngine
            
            # Choose the best available engine
            try:
                # Try Coqui for high quality
                engine = CoquiEngine()
                logger.info("Using CoquiEngine for RealtimeTTS")
            except Exception:
                # Fallback to system engine
                engine = SystemEngine()
                logger.info("Using SystemEngine for RealtimeTTS")
            
            self.engine = TextToAudioStream(engine)
            
        except ImportError:
            raise ImportError("RealtimeTTS not installed. Run: pip install realtimetts[all]")
    
    def _init_piper(self):
        """Initialize Piper TTS engine"""
        try:
            import piper
            # Piper initialization would go here
            # This is a placeholder for Piper integration
            logger.warning("Piper engine not fully implemented yet")
            raise NotImplementedError("Piper engine integration pending")
            
        except ImportError:
            raise ImportError("Piper not installed. Run: pip install piper-tts")
    
    def _init_chattts(self):
        """Initialize ChatTTS engine"""
        try:
            import ChatTTS
            import torch
            
            self.engine = ChatTTS.Chat()
            self.engine.load(compile=False)  # Set to True for better performance
            logger.info("ChatTTS engine loaded")
            
        except ImportError:
            raise ImportError("ChatTTS not installed. Run: pip install ChatTTS")
    
    def _init_system(self):
        """Initialize system TTS engine"""
        try:
            import pyttsx3
            self.engine = pyttsx3.init()
            
            # Configure voice properties
            if self.config.voice:
                voices = self.engine.getProperty('voices')
                for voice in voices:
                    if self.config.voice.lower() in voice.name.lower():
                        self.engine.setProperty('voice', voice.id)
                        break
            
            self.engine.setProperty('rate', int(150 * self.config.speed))
            self.engine.setProperty('volume', self.config.volume)
            
        except ImportError:
            raise ImportError("pyttsx3 not installed. Run: pip install pyttsx3")
    
    def _get_cache_key(self, text: str) -> str:
        """Generate cache key for text"""
        config_str = f"{self.config.engine.value}_{self.config.voice}_{self.config.speed}_{self.config.quality}"
        return hashlib.md5(f"{text}_{config_str}".encode()).hexdigest()
    
    def _get_cached_audio(self, text: str) -> Optional[str]:
        """Get cached audio file path if exists"""
        if not self.config.enable_cache:
            return None
            
        cache_key = self._get_cache_key(text)
        cache_file = self.cache_dir / f"{cache_key}.wav"
        
        if cache_file.exists():
            logger.debug(f"Using cached audio: {cache_file}")
            return str(cache_file)
        
        return None
    
    def _cache_audio(self, text: str, audio_path: str):
        """Cache audio file"""
        if not self.config.enable_cache:
            return
            
        cache_key = self._get_cache_key(text)
        cache_file = self.cache_dir / f"{cache_key}.wav"
        
        try:
            import shutil
            shutil.copy2(audio_path, cache_file)
            logger.debug(f"Cached audio: {cache_file}")
        except Exception as e:
            logger.warning(f"Failed to cache audio: {e}")
    
    def speak(self, text: str, blocking: bool = True) -> Optional[str]:
        """
        Convert text to speech and play it.
        
        Args:
            text: Text to convert to speech
            blocking: If True, wait for speech to complete
            
        Returns:
            Path to generated audio file (if applicable)
        """
        if not text.strip():
            logger.warning("Empty text provided")
            return None
        
        # Check cache first
        cached_path = self._get_cached_audio(text)
        if cached_path:
            self._play_audio_file(cached_path, blocking)
            return cached_path
        
        try:
            if self.config.engine == TTSEngine.REALTIMETTS:
                return self._speak_realtimetts(text, blocking)
            elif self.config.engine == TTSEngine.CHATTTS:
                return self._speak_chattts(text, blocking)
            elif self.config.engine == TTSEngine.SYSTEM:
                return self._speak_system(text, blocking)
            else:
                raise NotImplementedError(f"Engine {self.config.engine} not implemented")
                
        except Exception as e:
            logger.error(f"TTS error: {e}")
            return None
    
    def _speak_realtimetts(self, text: str, blocking: bool) -> Optional[str]:
        """Speak using RealtimeTTS"""
        self.engine.feed(text)
        
        if blocking:
            self.engine.play()
        else:
            self.engine.play_async()
        
        return None  # RealtimeTTS doesn't return file paths
    
    def _speak_chattts(self, text: str, blocking: bool) -> Optional[str]:
        """Speak using ChatTTS"""
        import torch
        import torchaudio
        
        wavs = self.engine.infer([text])
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
        temp_path = temp_file.name
        temp_file.close()
        
        try:
            torchaudio.save(temp_path, torch.from_numpy(wavs[0]).unsqueeze(0), self.config.sample_rate)
        except:
            torchaudio.save(temp_path, torch.from_numpy(wavs[0]), self.config.sample_rate)
        
        # Cache the audio
        self._cache_audio(text, temp_path)
        
        # Play the audio
        self._play_audio_file(temp_path, blocking)
        
        return temp_path
    
    def _speak_system(self, text: str, blocking: bool) -> Optional[str]:
        """Speak using system TTS"""
        if blocking:
            self.engine.say(text)
            self.engine.runAndWait()
        else:
            def speak_async():
                self.engine.say(text)
                self.engine.runAndWait()
            
            thread = threading.Thread(target=speak_async, daemon=True)
            thread.start()
        
        return None
    
    def _play_audio_file(self, file_path: str, blocking: bool):
        """Play audio file using system player"""
        try:
            if sys.platform.startswith('linux'):
                import subprocess
                cmd = ['aplay', file_path] if blocking else ['aplay', file_path, '&']
                if blocking:
                    subprocess.run(cmd, check=True, capture_output=True)
                else:
                    subprocess.Popen(cmd)
            elif sys.platform == 'darwin':
                import subprocess
                cmd = ['afplay', file_path]
                if blocking:
                    subprocess.run(cmd, check=True)
                else:
                    subprocess.Popen(cmd)
            elif sys.platform.startswith('win'):
                import winsound
                if blocking:
                    winsound.PlaySound(file_path, winsound.SND_FILENAME)
                else:
                    winsound.PlaySound(file_path, winsound.SND_FILENAME | winsound.SND_ASYNC)
        except Exception as e:
            logger.error(f"Failed to play audio: {e}")
    
    def speak_async(self, text: str) -> Optional[str]:
        """
        Convert text to speech and play it asynchronously.
        
        Args:
            text: Text to convert to speech
            
        Returns:
            Path to generated audio file (if applicable)
        """
        return self.speak(text, blocking=False)
    
    def stop(self):
        """Stop current speech"""
        try:
            if self.config.engine == TTSEngine.REALTIMETTS and self.engine:
                self.engine.stop()
            elif self.config.engine == TTSEngine.SYSTEM and self.engine:
                self.engine.stop()
        except Exception as e:
            logger.error(f"Error stopping TTS: {e}")
    
    def set_voice(self, voice: str):
        """Change voice (engine-dependent)"""
        self.config.voice = voice
        if self.config.engine == TTSEngine.SYSTEM:
            voices = self.engine.getProperty('voices')
            for v in voices:
                if voice.lower() in v.name.lower():
                    self.engine.setProperty('voice', v.id)
                    break
    
    def set_speed(self, speed: float):
        """Change speech speed"""
        self.config.speed = speed
        if self.config.engine == TTSEngine.SYSTEM:
            self.engine.setProperty('rate', int(150 * speed))
    
    def set_volume(self, volume: float):
        """Change speech volume (0.0 to 1.0)"""
        self.config.volume = max(0.0, min(1.0, volume))
        if self.config.engine == TTSEngine.SYSTEM:
            self.engine.setProperty('volume', self.config.volume)
    
    def get_available_voices(self) -> list:
        """Get list of available voices"""
        if self.config.engine == TTSEngine.SYSTEM:
            return [voice.name for voice in self.engine.getProperty('voices')]
        return []
    
    def clear_cache(self):
        """Clear TTS cache"""
        if self.config.enable_cache and self.cache_dir.exists():
            import shutil
            shutil.rmtree(self.cache_dir)
            self.cache_dir.mkdir(exist_ok=True)
            logger.info("TTS cache cleared")


# Convenience functions for easy usage
def create_tts_engine(engine: str = "realtimetts", **kwargs) -> HighPerformanceTTS:
    """
    Create a TTS engine with simplified configuration.
    
    Args:
        engine: Engine type ("realtimetts", "piper", "chattts", "system")
        **kwargs: Additional configuration options
        
    Returns:
        Configured TTS engine
    """
    config = TTSConfig(engine=TTSEngine(engine), **kwargs)
    return HighPerformanceTTS(config)


def quick_speak(text: str, engine: str = "realtimetts", **kwargs):
    """
    Quick text-to-speech function for simple usage.
    
    Args:
        text: Text to speak
        engine: Engine type
        **kwargs: Additional options
    """
    tts = create_tts_engine(engine, **kwargs)
    tts.speak(text)


if __name__ == "__main__":
    # Example usage and testing
    import argparse
    
    parser = argparse.ArgumentParser(description="High-Performance TTS Engine")
    parser.add_argument("text", nargs="?", default="Hello, this is a test of the high-performance TTS engine.", 
                       help="Text to speak")
    parser.add_argument("--engine", choices=["realtimetts", "piper", "chattts", "system"], 
                       default="realtimetts", help="TTS engine to use")
    parser.add_argument("--voice", help="Voice to use")
    parser.add_argument("--speed", type=float, default=1.0, help="Speech speed")
    parser.add_argument("--volume", type=float, default=1.0, help="Speech volume")
    parser.add_argument("--async-mode", action="store_true", help="Use async playback")
    parser.add_argument("--list-voices", action="store_true", help="List available voices")
    
    args = parser.parse_args()
    
    try:
        # Create TTS engine
        config = TTSConfig(
            engine=TTSEngine(args.engine),
            voice=args.voice,
            speed=args.speed,
            volume=args.volume
        )
        
        tts = HighPerformanceTTS(config)
        
        if args.list_voices:
            voices = tts.get_available_voices()
            print("Available voices:")
            for voice in voices:
                print(f"  - {voice}")
        else:
            print(f"Speaking with {args.engine}: {args.text}")
            if args.async_mode:
                tts.speak_async(args.text)
                time.sleep(5)  # Wait a bit for async playback
            else:
                tts.speak(args.text)
            
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)
