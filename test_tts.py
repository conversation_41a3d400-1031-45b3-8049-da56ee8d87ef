#!/usr/bin/env python3
"""
Simple TTS Test Script
"""

def test_simple_tts():
    """Test the simple TTS engine"""
    print("Testing Simple TTS Engine...")

    try:
        from simple_tts import quick_speak, create_tts

        # Simple test
        print("1. Basic speech test...")
        success = quick_speak("Hello! Simple TTS engine is working correctly.")
        print(f"   Result: {'✅ Success' if success else '❌ Failed'}")

        # Test with different settings
        print("2. Custom settings test...")
        tts = create_tts(speed=1.2, volume=0.8)
        success = tts.speak("This is a test with custom speed and volume.")
        print(f"   Result: {'✅ Success' if success else '❌ Failed'}")

        print("Simple TTS test completed!")
        return True

    except Exception as e:
        print(f"Simple TTS test failed: {e}")
        return False

def test_advanced_tts():
    """Test the advanced TTS engine"""
    print("\nTesting Advanced TTS Engine...")

    try:
        from tts_engine import quick_speak, create_tts_engine

        # Simple test
        print("1. Basic speech test...")
        quick_speak("Hello! Advanced TTS engine is working correctly.")

        # Test with different settings
        print("2. Custom settings test...")
        tts = create_tts_engine("system", speed=1.2, volume=0.8)
        tts.speak("This is a test with custom speed and volume.")

        print("Advanced TTS test completed!")
        return True

    except Exception as e:
        print(f"Advanced TTS test failed: {e}")
        return False

def main():
    print("TTS Engine Test Suite")
    print("====================")

    # Test simple TTS (should always work)
    simple_success = test_simple_tts()

    # Test advanced TTS (may fail if dependencies missing)
    advanced_success = test_advanced_tts()

    print("\n" + "="*40)
    print("Test Results:")
    print(f"Simple TTS:   {'✅ PASS' if simple_success else '❌ FAIL'}")
    print(f"Advanced TTS: {'✅ PASS' if advanced_success else '❌ FAIL'}")

    if simple_success:
        print("\n✅ At least one TTS engine is working!")
        print("You can use simple_tts.py for reliable TTS functionality.")
    else:
        print("\n❌ No TTS engines are working.")
        print("Please check your system audio configuration.")

if __name__ == "__main__":
    main()
