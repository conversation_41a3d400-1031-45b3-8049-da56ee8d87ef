#!/usr/bin/env python3
"""
Simple TTS Test Script
"""

from tts_engine import quick_speak, create_tts_engine

def main():
    print("Testing TTS Engine...")
    
    # Simple test
    quick_speak("Hello! TTS engine is working correctly.")
    
    # Test with different settings
    tts = create_tts_engine("system", speed=1.2, volume=0.8)
    tts.speak("This is a test with custom speed and volume.")
    
    print("TTS test completed!")

if __name__ == "__main__":
    main()
